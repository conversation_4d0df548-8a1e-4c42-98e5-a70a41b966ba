// ==UserScript==
// @name         Ollama智能翻译助手
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  复制文字后自动弹出翻译框，连接ollama本地模型
// <AUTHOR>
// @match        *://*/*
// @grant        GM_setValue
// @grant        GM_getValue
// @grant        GM_xmlhttpRequest
// @run-at       document-end
// ==/UserScript==

(function() {
    'use strict';

    // 默认配置
    const DEFAULT_CONFIG = {
        ollamaUrl: 'http://localhost:11434',
        model: 'llama3.2',
        prompt: '请将以下文本翻译成中文，如果已经是中文则翻译成英文：',
        autoShow: true,
        minTextLength: 3
    };

    // 获取配置
    function getConfig() {
        const config = {};
        Object.keys(DEFAULT_CONFIG).forEach(key => {
            config[key] = GM_getValue(key, DEFAULT_CONFIG[key]);
        });
        return config;
    }

    // 保存配置
    function saveConfig(config) {
        Object.keys(config).forEach(key => {
            GM_setValue(key, config[key]);
        });
    }

    let config = getConfig();
    let translationBox = null;
    let isProcessing = false;

    // 创建翻译框
    function createTranslationBox() {
        const box = document.createElement('div');
        box.id = 'ollama-translation-box';
        box.innerHTML = `
            <div class="translation-header">
                <span>Ollama翻译助手</span>
                <div class="translation-controls">
                    <button id="settings-btn" title="设置">⚙️</button>
                    <button id="close-btn" title="关闭">✕</button>
                </div>
            </div>
            <div class="translation-content">
                <div class="input-section">
                    <label>原文：</label>
                    <textarea id="source-text" placeholder="在此输入或复制文本..."></textarea>
                </div>
                <div class="output-section">
                    <label>译文：</label>
                    <div id="translation-result">等待翻译...</div>
                </div>
                <div class="button-section">
                    <button id="translate-btn">翻译</button>
                    <button id="copy-result-btn">复制结果</button>
                </div>
            </div>
        `;

        // 添加样式
        const style = document.createElement('style');
        style.textContent = `
            #ollama-translation-box {
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                width: 500px;
                max-width: 90vw;
                background: white;
                border: 2px solid #4CAF50;
                border-radius: 10px;
                box-shadow: 0 4px 20px rgba(0,0,0,0.3);
                z-index: 10000;
                font-family: Arial, sans-serif;
                display: none;
            }
            
            .translation-header {
                background: #4CAF50;
                color: white;
                padding: 10px 15px;
                border-radius: 8px 8px 0 0;
                display: flex;
                justify-content: space-between;
                align-items: center;
                cursor: move;
            }
            
            .translation-controls button {
                background: none;
                border: none;
                color: white;
                cursor: pointer;
                padding: 5px;
                margin-left: 5px;
                border-radius: 3px;
            }
            
            .translation-controls button:hover {
                background: rgba(255,255,255,0.2);
            }
            
            .translation-content {
                padding: 15px;
            }
            
            .input-section, .output-section {
                margin-bottom: 15px;
            }
            
            .input-section label, .output-section label {
                display: block;
                margin-bottom: 5px;
                font-weight: bold;
                color: #333;
            }
            
            #source-text {
                width: 100%;
                height: 80px;
                padding: 8px;
                border: 1px solid #ddd;
                border-radius: 4px;
                resize: vertical;
                font-size: 14px;
                box-sizing: border-box;
            }
            
            #translation-result {
                width: 100%;
                min-height: 80px;
                padding: 8px;
                border: 1px solid #ddd;
                border-radius: 4px;
                background: #f9f9f9;
                font-size: 14px;
                line-height: 1.4;
                white-space: pre-wrap;
                box-sizing: border-box;
            }
            
            .button-section {
                display: flex;
                gap: 10px;
                justify-content: center;
            }
            
            .button-section button {
                padding: 8px 16px;
                border: none;
                border-radius: 4px;
                cursor: pointer;
                font-size: 14px;
            }
            
            #translate-btn {
                background: #4CAF50;
                color: white;
            }
            
            #translate-btn:hover {
                background: #45a049;
            }
            
            #translate-btn:disabled {
                background: #ccc;
                cursor: not-allowed;
            }
            
            #copy-result-btn {
                background: #2196F3;
                color: white;
            }
            
            #copy-result-btn:hover {
                background: #1976D2;
            }
            
            .settings-panel {
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                width: 400px;
                background: white;
                border: 2px solid #2196F3;
                border-radius: 10px;
                box-shadow: 0 4px 20px rgba(0,0,0,0.3);
                z-index: 10001;
                padding: 20px;
                display: none;
            }
            
            .settings-panel h3 {
                margin-top: 0;
                color: #2196F3;
            }
            
            .setting-item {
                margin-bottom: 15px;
            }
            
            .setting-item label {
                display: block;
                margin-bottom: 5px;
                font-weight: bold;
            }
            
            .setting-item input, .setting-item textarea {
                width: 100%;
                padding: 8px;
                border: 1px solid #ddd;
                border-radius: 4px;
                box-sizing: border-box;
            }
            
            .setting-item textarea {
                height: 60px;
                resize: vertical;
            }
            
            .settings-buttons {
                display: flex;
                gap: 10px;
                justify-content: center;
                margin-top: 20px;
            }
            
            .settings-buttons button {
                padding: 8px 16px;
                border: none;
                border-radius: 4px;
                cursor: pointer;
            }
            
            .save-settings {
                background: #4CAF50;
                color: white;
            }
            
            .cancel-settings {
                background: #f44336;
                color: white;
            }
        `;
        
        document.head.appendChild(style);
        document.body.appendChild(box);

        return box;
    }

    // 创建设置面板
    function createSettingsPanel() {
        const panel = document.createElement('div');
        panel.className = 'settings-panel';
        panel.id = 'settings-panel';
        panel.innerHTML = `
            <h3>设置</h3>
            <div class="setting-item">
                <label>Ollama服务地址:</label>
                <input type="text" id="ollama-url" value="${config.ollamaUrl}">
            </div>
            <div class="setting-item">
                <label>模型名称:</label>
                <input type="text" id="model-name" value="${config.model}">
            </div>
            <div class="setting-item">
                <label>提示词:</label>
                <textarea id="prompt-text">${config.prompt}</textarea>
            </div>
            <div class="setting-item">
                <label>
                    <input type="checkbox" id="auto-show" ${config.autoShow ? 'checked' : ''}>
                    复制时自动显示
                </label>
            </div>
            <div class="setting-item">
                <label>最小文本长度:</label>
                <input type="number" id="min-length" value="${config.minTextLength}" min="1">
            </div>
            <div class="settings-buttons">
                <button class="save-settings">保存</button>
                <button class="cancel-settings">取消</button>
            </div>
        `;
        document.body.appendChild(panel);
        return panel;
    }

    // 调用Ollama API
    async function callOllama(text) {
        return new Promise((resolve, reject) => {
            const requestData = {
                model: config.model,
                prompt: config.prompt + '\n\n' + text,
                stream: false
            };

            GM_xmlhttpRequest({
                method: 'POST',
                url: config.ollamaUrl + '/api/generate',
                headers: {
                    'Content-Type': 'application/json'
                },
                data: JSON.stringify(requestData),
                timeout: 30000,
                onload: function(response) {
                    try {
                        const result = JSON.parse(response.responseText);
                        if (result.response) {
                            resolve(result.response.trim());
                        } else {
                            reject('API返回格式错误');
                        }
                    } catch (e) {
                        reject('解析响应失败: ' + e.message);
                    }
                },
                onerror: function(error) {
                    reject('请求失败，请检查Ollama服务是否运行');
                },
                ontimeout: function() {
                    reject('请求超时');
                }
            });
        });
    }

    // 执行翻译
    async function performTranslation(text) {
        if (isProcessing) return;

        isProcessing = true;
        const resultDiv = document.getElementById('translation-result');
        const translateBtn = document.getElementById('translate-btn');

        resultDiv.textContent = '翻译中...';
        translateBtn.disabled = true;
        translateBtn.textContent = '翻译中...';

        try {
            const translation = await callOllama(text);
            resultDiv.textContent = translation;
        } catch (error) {
            resultDiv.textContent = '翻译失败: ' + error;
        } finally {
            isProcessing = false;
            translateBtn.disabled = false;
            translateBtn.textContent = '翻译';
        }
    }

    // 显示翻译框
    function showTranslationBox(text = '') {
        if (!translationBox) {
            translationBox = createTranslationBox();
            setupEventListeners();
        }

        const sourceText = document.getElementById('source-text');
        const resultDiv = document.getElementById('translation-result');

        if (text) {
            sourceText.value = text;
            resultDiv.textContent = '点击翻译按钮开始翻译...';
        }

        translationBox.style.display = 'block';
        sourceText.focus();

        // 如果有文本且长度符合要求，自动翻译
        if (text && text.length >= config.minTextLength) {
            setTimeout(() => performTranslation(text), 100);
        }
    }

    // 隐藏翻译框
    function hideTranslationBox() {
        if (translationBox) {
            translationBox.style.display = 'none';
        }
    }

    // 设置事件监听器
    function setupEventListeners() {
        // 关闭按钮
        document.getElementById('close-btn').addEventListener('click', hideTranslationBox);

        // 翻译按钮
        document.getElementById('translate-btn').addEventListener('click', () => {
            const text = document.getElementById('source-text').value.trim();
            if (text) {
                performTranslation(text);
            }
        });

        // 复制结果按钮
        document.getElementById('copy-result-btn').addEventListener('click', () => {
            const result = document.getElementById('translation-result').textContent;
            if (result && result !== '等待翻译...' && result !== '翻译中...') {
                navigator.clipboard.writeText(result).then(() => {
                    const btn = document.getElementById('copy-result-btn');
                    const originalText = btn.textContent;
                    btn.textContent = '已复制!';
                    setTimeout(() => {
                        btn.textContent = originalText;
                    }, 1000);
                });
            }
        });

        // 设置按钮
        document.getElementById('settings-btn').addEventListener('click', showSettings);

        // 拖拽功能
        let isDragging = false;
        let dragOffset = { x: 0, y: 0 };

        const header = document.querySelector('.translation-header');
        header.addEventListener('mousedown', (e) => {
            isDragging = true;
            const rect = translationBox.getBoundingClientRect();
            dragOffset.x = e.clientX - rect.left;
            dragOffset.y = e.clientY - rect.top;
        });

        document.addEventListener('mousemove', (e) => {
            if (isDragging) {
                translationBox.style.left = (e.clientX - dragOffset.x) + 'px';
                translationBox.style.top = (e.clientY - dragOffset.y) + 'px';
                translationBox.style.transform = 'none';
            }
        });

        document.addEventListener('mouseup', () => {
            isDragging = false;
        });

        // 回车键翻译
        document.getElementById('source-text').addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && e.ctrlKey) {
                e.preventDefault();
                const text = e.target.value.trim();
                if (text) {
                    performTranslation(text);
                }
            }
        });
    }

    // 显示设置面板
    function showSettings() {
        let settingsPanel = document.getElementById('settings-panel');
        if (!settingsPanel) {
            settingsPanel = createSettingsPanel();
            setupSettingsListeners(settingsPanel);
        }

        // 更新当前配置值
        document.getElementById('ollama-url').value = config.ollamaUrl;
        document.getElementById('model-name').value = config.model;
        document.getElementById('prompt-text').value = config.prompt;
        document.getElementById('auto-show').checked = config.autoShow;
        document.getElementById('min-length').value = config.minTextLength;

        settingsPanel.style.display = 'block';
    }

    // 设置面板事件监听器
    function setupSettingsListeners(panel) {
        panel.querySelector('.save-settings').addEventListener('click', () => {
            config.ollamaUrl = document.getElementById('ollama-url').value.trim();
            config.model = document.getElementById('model-name').value.trim();
            config.prompt = document.getElementById('prompt-text').value.trim();
            config.autoShow = document.getElementById('auto-show').checked;
            config.minTextLength = parseInt(document.getElementById('min-length').value) || 3;

            saveConfig(config);
            panel.style.display = 'none';

            // 显示保存成功提示
            const btn = panel.querySelector('.save-settings');
            const originalText = btn.textContent;
            btn.textContent = '已保存!';
            setTimeout(() => {
                btn.textContent = originalText;
            }, 1000);
        });

        panel.querySelector('.cancel-settings').addEventListener('click', () => {
            panel.style.display = 'none';
        });
    }

    // 监听复制事件
    let lastCopiedText = '';
    let lastSelectionText = '';

    // 方法1: 监听copy事件并获取选中文本
    document.addEventListener('copy', (e) => {
        if (!config.autoShow) return;

        // 获取当前选中的文本
        const selection = window.getSelection();
        let copiedText = '';

        if (selection && selection.toString()) {
            copiedText = selection.toString().trim();
        } else if (e.clipboardData && e.clipboardData.getData) {
            copiedText = e.clipboardData.getData('text/plain').trim();
        }

        if (copiedText &&
            copiedText.length >= config.minTextLength &&
            copiedText !== lastCopiedText &&
            copiedText.length < 5000) {

            lastCopiedText = copiedText;
            console.log('检测到复制文本:', copiedText.substring(0, 50) + '...');

            // 延迟显示，避免干扰复制操作
            setTimeout(() => {
                showTranslationBox(copiedText);
            }, 200);
        }
    });

    // 方法2: 监听选择变化（备用方案）
    document.addEventListener('selectionchange', () => {
        if (!config.autoShow) return;

        const selection = window.getSelection();
        if (selection && selection.toString()) {
            lastSelectionText = selection.toString().trim();
        }
    });

    // 统一的键盘事件监听器
    document.addEventListener('keydown', (e) => {
        // 方法3: 监听键盘复制快捷键
        if (config.autoShow && (e.ctrlKey || e.metaKey) && e.key === 'c') {
            setTimeout(() => {
                if (lastSelectionText &&
                    lastSelectionText.length >= config.minTextLength &&
                    lastSelectionText !== lastCopiedText &&
                    lastSelectionText.length < 5000) {

                    lastCopiedText = lastSelectionText;
                    console.log('通过快捷键检测到复制:', lastSelectionText.substring(0, 50) + '...');
                    showTranslationBox(lastSelectionText);
                }
            }, 100);
            return;
        }

        // Ctrl+Shift+T 显示翻译框
        if (e.ctrlKey && e.shiftKey && e.key === 'T') {
            e.preventDefault();
            console.log('快捷键打开翻译框');
            showTranslationBox();
            return;
        }

        // ESC 关闭翻译框
        if (e.key === 'Escape') {
            hideTranslationBox();
            const settingsPanel = document.getElementById('settings-panel');
            if (settingsPanel) {
                settingsPanel.style.display = 'none';
            }
            return;
        }
    });

    // 初始化
    function init() {
        console.log('Ollama翻译助手已加载');
        console.log('快捷键: Ctrl+Shift+T 打开翻译框');
        console.log('配置:', config);

        // 创建一个小的状态指示器
        const indicator = document.createElement('div');
        indicator.innerHTML = '🌐';
        indicator.title = 'Ollama翻译助手已启用\n快捷键: Ctrl+Shift+T';
        indicator.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            width: 30px;
            height: 30px;
            background: #4CAF50;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            cursor: pointer;
            font-size: 16px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
        `;

        indicator.addEventListener('click', () => showTranslationBox());
        indicator.addEventListener('mouseenter', () => {
            indicator.style.transform = 'scale(1.1)';
        });
        indicator.addEventListener('mouseleave', () => {
            indicator.style.transform = 'scale(1)';
        });

        document.body.appendChild(indicator);

        // 3秒后隐藏指示器
        setTimeout(() => {
            indicator.style.opacity = '0.3';
        }, 3000);
    }

    // 页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

})();
