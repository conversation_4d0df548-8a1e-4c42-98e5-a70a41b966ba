// ==UserScript==
// @name         AI翻译助手 (中央显示版 + 追问功能+原文显示)
// @namespace    http://tampermonkey.net/
// @version      1.5
// @description  选中文字后翻译和解释，支持自定义提示词和多种AI模型，弹出框显示在屏幕中央，可追问
// <AUTHOR> on Pollinations
// @match        *://*/*
// @grant        GM_addStyle
// @grant        GM_xmlhttpRequest
// @grant        GM_setValue
// @grant        GM_getValue
// ==/UserScript==

(function() {
    'use strict';

    // 添加样式
    GM_addStyle(`
        #translate-button {
            position: absolute;
            background-color: #4285f4;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 5px 10px;
            font-size: 14px;
            cursor: pointer;
            z-index: 10000;
            font-family: Arial, sans-serif;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            display: none;
        }
        #translate-button:hover {
            background-color: #3367d6;
        }
        #translation-container {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background-color: #fff;
            color: #333;
            padding: 12px;
            border-radius: 4px;
            width: 500px;
            max-width: 80vw;
            max-height: 80vh;
            overflow-y: auto;
            z-index: 10001;
            font-family: Arial, sans-serif;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
            border: 1px solid #ddd;
            display: none;
            font-size: 14px;
            line-height: 1.4;
        }
        #translation-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            padding-bottom: 8px;
            border-bottom: 1px solid #eee;
        }
        #model-selector {
            padding: 4px;
            border-radius: 3px;
            border: 1px solid #ddd;
            background-color: #f8f8f8;
            font-size: 12px;
        }
        #original-text {
            background-color: #f7f9fc;
            border-left: 3px solid #4285f4;
            padding: 10px 12px;
            margin-bottom: 12px;
            font-size: 14px;
            line-height: 1.5;
            border-radius: 0 4px 4px 0;
            max-height: 150px;
            overflow-y: auto;
            word-break: break-word;
            white-space: pre-wrap;
        }
        #translation-content {
            margin-bottom: 10px;
            min-height: 40px;
        }
        #translation-footer {
            font-size: 11px;
            color: #777;
            margin-top: 8px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        #translation-close, #settings-btn {
            background: none;
            border: none;
            color: #888;
            font-size: 16px;
            cursor: pointer;
            padding: 0;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        #translation-close:hover, #settings-btn:hover {
            color: #333;
        }
        #retranslate-btn {
            background-color: #f1f1f1;
            border: 1px solid #ddd;
            border-radius: 3px;
            padding: 3px 8px;
            font-size: 12px;
            cursor: pointer;
            color: #444;
        }
        #retranslate-btn:hover {
            background-color: #e8e8e8;
        }
        .translation-loading {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid rgba(66,133,244,.3);
            border-radius: 50%;
            border-top-color: #4285f4;
            animation: translation-spin 1s linear infinite;
            margin-right: 8px;
        }
        @keyframes translation-spin {
            to { transform: rotate(360deg); }
        }

        /* 设置面板样式 */
        #settings-panel {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            z-index: 10002;
            width: 500px;
            max-width: 90vw;
            display: none;
        }
        #settings-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }
        #settings-header h3 {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
        }
        #close-settings {
            background: none;
            border: none;
            font-size: 18px;
            cursor: pointer;
            color: #777;
        }
        #close-settings:hover {
            color: #333;
        }
        #prompt-textarea {
            width: 100%;
            min-height: 150px;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            resize: vertical;
            font-family: Arial, sans-serif;
            font-size: 13px;
            margin-bottom: 15px;
        }
        #settings-buttons {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }
        #save-settings, #reset-prompt {
            padding: 6px 12px;
            border-radius: 4px;
            border: none;
            font-size: 13px;
            cursor: pointer;
        }
        #save-settings {
            background-color: #4285f4;
            color: white;
        }
        #save-settings:hover {
            background-color: #3367d6;
        }
        #reset-prompt {
            background-color: #f1f1f1;
            color: #444;
            border: 1px solid #ddd;
        }
        #reset-prompt:hover {
            background-color: #e8e8e8;
        }

        /* 添加模态背景 */
        #modal-backdrop {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.4);
            z-index: 10000;
            display: none;
        }

        /* 追问输入框样式 */
        #followup-container {
            display: flex;
            margin-top: 15px;
            border-top: 1px solid #eee;
            padding-top: 12px;
        }
        #followup-input {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            outline: none;
            font-family: Arial, sans-serif;
        }
        #followup-input:focus {
            border-color: #4285f4;
            box-shadow: 0 0 0 2px rgba(66,133,244,0.2);
        }
        #followup-button {
            background-color: #4285f4;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 8px 16px;
            margin-left: 8px;
            font-size: 14px;
            cursor: pointer;
        }
        #followup-button:hover {
            background-color: #3367d6;
        }
        #followup-button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        .conversation-entry {
            margin-bottom: 12px;
            padding-bottom: 12px;
            border-bottom: 1px dashed #eee;
        }
        .user-question {
            background-color: #f8f9fa;
            padding: 8px 12px;
            border-radius: 4px;
            margin-bottom: 8px;
            font-weight: bold;
        }
        .ai-response {
            padding-left: 4px;
        }
    `);

    // 默认提示词
    const DEFAULT_PROMPT = `你是一个翻译和语言解释助手。根据用户提供的文本类型执行不同任务：
1. 如果是英文或其他非中文的长文本，请遵循以下步骤：
   1. 提供完整的中文翻译
   2. 提供原文想要表达的意思
2. 如果是英文单词或短语，提供：
   - 中文翻译
   - 音标（如果是单词）
   - 详细解释（词性、含义等）
   - 3个英文例句及其中文翻译
3. 如果是中文文本，直接解释说明就行了

请用中文回答。`;

    // 创建模态背景
    const modalBackdrop = document.createElement('div');
    modalBackdrop.id = 'modal-backdrop';
    document.body.appendChild(modalBackdrop);

    // 创建翻译按钮
    const button = document.createElement('button');
    button.id = 'translate-button';
    button.textContent = '翻译';
    document.body.appendChild(button);

    // 创建翻译结果容器
    const translationContainer = document.createElement('div');
    translationContainer.id = 'translation-container';
    translationContainer.innerHTML = `
        <div id="translation-header">
            <select id="model-selector">
                <option value="loading">加载模型中...</option>
            </select>
            <div>
                <button id="settings-btn" title="设置提示词">⚙️</button>
                <button id="translation-close">×</button>
            </div>
        </div>
        <div id="original-text"></div>
        <div id="translation-content"></div>
        <div id="translation-footer">
            <button id="retranslate-btn">重新生成</button>
            <span>Powered by Pollinations.ai</span>
        </div>
        <div id="followup-container">
            <input type="text" id="followup-input" placeholder="输入您的问题...(按回车键直接发送)">
            <button id="followup-button">追问</button>
        </div>
    `;
    document.body.appendChild(translationContainer);

    // 创建设置面板
    const settingsPanel = document.createElement('div');
    settingsPanel.id = 'settings-panel';
    settingsPanel.innerHTML = `
        <div id="settings-header">
            <h3>提示词设置</h3>
            <button id="close-settings">×</button>
        </div>
        <div>
            <p style="margin-top: 0; margin-bottom: 8px; font-size: 13px; color: #555;">自定义AI的提示词，告诉AI如何处理你的文本：</p>
            <textarea id="prompt-textarea" placeholder="输入自定义提示词...">${DEFAULT_PROMPT}</textarea>
        </div>
        <div id="settings-buttons">
            <button id="reset-prompt">恢复默认</button>
            <button id="save-settings">保存设置</button>
        </div>
    `;
    document.body.appendChild(settingsPanel);

    let lastSelectedText = '';
    let lastSelectionRect = null;
    let availableModels = [];
    let conversationHistory = []; // 存储对话历史

    // 获取保存的提示词或使用默认提示词
    const savedPrompt = GM_getValue('custom_prompt', DEFAULT_PROMPT);
    document.getElementById('prompt-textarea').value = savedPrompt;

    // 页面加载时获取可用模型
    fetchAvailableModels();

    // 获取Pollinations.ai可用的模型列表
    function fetchAvailableModels() {
        // 尝试从缓存获取模型列表
        const cachedModels = GM_getValue('pollinations_models');
        const cacheTimestamp = GM_getValue('pollinations_models_timestamp');

        // 如果有缓存且不超过1天，使用缓存
        if (cachedModels && cacheTimestamp && (Date.now() - cacheTimestamp < 86400000)) {
            availableModels = JSON.parse(cachedModels);
            updateModelSelector(availableModels);
            return;
        }

        GM_xmlhttpRequest({
            method: 'GET',
            url: 'https://text.pollinations.ai/models',
            timeout: 10000,
            onload: function(response) {
                if (response.status === 200) {
                    try {
                        // 解析API返回的模型列表
                        const data = JSON.parse(response.responseText);

                        // 提取文本模型 - 格式可能因API而异，需要针对实际响应调整
                        // 这是一个假设的处理方式，实际结构可能不同
                        let models = [];

                        // 如果是数组格式
                        if (Array.isArray(data)) {
                            models = data.map(model => {
                                return {
                                    id: typeof model === 'string' ? model : model.id || model.name,
                                    name: model.name || model.id || model,
                                    description: model.description || ''
                                };
                            });
                        }
                        // 如果是对象格式，提取键作为模型ID
                        else if (typeof data === 'object') {
                            for (const key in data) {
                                if (Object.hasOwnProperty.call(data, key)) {
                                    models.push({
                                        id: key,
                                        name: data[key].name || key,
                                        description: data[key].description || ''
                                    });
                                }
                            }
                        }

                        // 过滤，优先保留文本模型
                        models = models.filter(model => {
                            const id = String(model.id).toLowerCase();
                            // 排除明确的音频模型
                            if (id.includes('audio') || id.includes('voice')) return false;
                            return true;
                        });

                        // 保存模型列表
                        availableModels = models;

                        // 缓存模型列表
                        GM_setValue('pollinations_models', JSON.stringify(availableModels));
                        GM_setValue('pollinations_models_timestamp', Date.now());

                        // 更新选择器
                        updateModelSelector(availableModels);
                    } catch (error) {
                        console.error('解析模型列表失败:', error);
                        setDefaultModels();
                    }
                } else {
                    console.error('获取模型列表失败，状态码:', response.status);
                    setDefaultModels();
                }
            },
            onerror: function(error) {
                console.error('获取模型列表请求失败:', error);
                setDefaultModels();
            },
            ontimeout: function() {
                console.error('获取模型列表请求超时');
                setDefaultModels();
            }
        });
    }

    // 设置默认模型列表（API请求失败时使用）
    function setDefaultModels() {
        availableModels = [
            { id: 'openai', name: 'OpenAI (默认)', description: 'OpenAI文本模型' },
            { id: 'mistral', name: 'Mistral', description: '开源大语言模型' },
            { id: 'claude-hybridspace', name: 'Claude', description: 'Anthropic Claude' }
        ];
        updateModelSelector(availableModels);
    }

    // 更新模型选择器
    function updateModelSelector(models) {
        const selector = document.getElementById('model-selector');
        if (!selector) return;

        // 清空现有选项
        selector.innerHTML = '';

        // 添加可用模型
        models.forEach(model => {
            const option = document.createElement('option');
            option.value = model.id;
            option.textContent = model.name;
            if (model.description) {
                option.title = model.description;
            }
            selector.appendChild(option);
        });

        // 选择保存的模型或默认模型
        const savedModel = GM_getValue('preferred_model');
        if (savedModel && models.some(m => m.id === savedModel)) {
            selector.value = savedModel;
        } else if (models.some(m => m.id === 'openai')) {
            selector.value = 'openai'; // 默认选择OpenAI
        }
    }

    // 使用Pollinations.ai API进行翻译并解释
    function processTextWithAI(text, modelId, isFollowup = false) {
        return new Promise((resolve, reject) => {
            // 保存用户偏好的模型
            GM_setValue('preferred_model', modelId);

            let aiPrompt;

            // 如果是追问，构建带上下文的提示词
            if (isFollowup && conversationHistory.length > 0) {
                // 构建对话历史
                let historyText = '';
                conversationHistory.forEach(entry => {
                    historyText += `用户: ${entry.question}\n`;
                    historyText += `AI: ${entry.answer}\n\n`;
                });

                aiPrompt = `你之前已经翻译和解释了一些内容。现在继续基于你之前的回答，回答用户的新问题。

                以下是历史对话:
                ${historyText}

                用户的新问题: "${text}"

                请用中文回答。`;
            } else {
                // 获取用户自定义的提示词
                const customPrompt = GM_getValue('custom_prompt', DEFAULT_PROMPT);

                // 构建AI处理提示
                aiPrompt = `${customPrompt}\n\n用户文本: "${text}"`;

                // 清空之前的对话历史
                conversationHistory = [];
            }

            const encodedPrompt = encodeURIComponent(aiPrompt);

            // 构建API URL
            const apiUrl = `https://text.pollinations.ai/${encodedPrompt}`;

            // 添加参数
            const params = {
                model: modelId,
                private: true // 设为私有，不出现在公共feed中
            };

            // 构建完整URL
            const fullUrl = `${apiUrl}?${new URLSearchParams(params).toString()}`;

            // 使用GM_xmlhttpRequest进行跨域请求
            const xhr = GM_xmlhttpRequest({
                method: 'GET',
                url: fullUrl,
                timeout: 20000, // 20秒超时
                onload: function(response) {
                    if (response.status === 200) {
                        // 获取AI响应
                        const aiResponse = response.responseText;

                        // 保存到对话历史
                        conversationHistory.push({
                            question: text,
                            answer: aiResponse
                        });

                        resolve(aiResponse);
                    } else {
                        reject(new Error(`请求失败，状态码：${response.status}`));
                    }
                },
                onerror: function(error) {
                    reject(new Error('网络请求失败'));
                },
                ontimeout: function() {
                    reject(new Error('请求超时，请稍后再试'));
                },
                onreadystatechange: function(response) {
                    if (response.readyState === 3 && response.status === 200) {
                        // 流式数据处理
                        const partialResponse = response.responseText;
                        // 这里触发流式输出回调
                        if (typeof this.onStreamData === 'function') {
                            this.onStreamData(partialResponse);
                        }
                    }
                }
            });

            // 添加流式数据处理方法
            xhr.onStreamData = null;
            return xhr;
        });
    }

    // 显示AI处理结果 - 现在显示在屏幕中央
    async function showAIResult(text, isFollowup = false) {
        const contentDiv = document.getElementById('translation-content');

        // 如果是追问，添加新的问题到内容中
        if (isFollowup) {
            // 添加问题到界面
            const questionElement = document.createElement('div');
            questionElement.className = 'user-question';
            questionElement.textContent = text;

            // 添加新的回答容器
            const responseElement = document.createElement('div');
            responseElement.className = 'ai-response';
            responseElement.innerHTML = '<span class="translation-loading"></span> AI正在处理中...';

            // 将提问和待回答容器包装在一个条目中
            const entryElement = document.createElement('div');
            entryElement.className = 'conversation-entry';
            entryElement.appendChild(questionElement);
            entryElement.appendChild(responseElement);

            contentDiv.appendChild(entryElement);

            // 滚动到底部
            translationContainer.scrollTop = translationContainer.scrollHeight;

            // 获取当前选择的模型
            const modelSelector = document.getElementById('model-selector');
            const selectedModel = modelSelector.value;

            try {
                // 获取AI处理结果
                const aiResult = await processTextWithAI(text, selectedModel, true);
                // 将结果中的换行符转换为HTML的<br>标签
                responseElement.innerHTML = aiResult.replace(/\n/g, '<br>');
                // 滚动到底部
                translationContainer.scrollTop = translationContainer.scrollHeight;
            } catch (error) {
                responseElement.innerHTML = `<span style="color: red;">❌ 处理出错: ${error.message}</span>`;
            }
        } else {
            // 首次翻译，清空内容区域
            contentDiv.innerHTML = '<span class="translation-loading"></span> AI正在处理中...';

            // 显示原文
            const originalTextDiv = document.getElementById('original-text');
            originalTextDiv.textContent = text;

            // 获取当前选择的模型
            const modelSelector = document.getElementById('model-selector');
            const selectedModel = modelSelector.value;

            // 显示模态背景和翻译容器
            modalBackdrop.style.display = 'block';
            translationContainer.style.display = 'block';

            try {
                // 获取AI处理结果
                const aiResult = await processTextWithAI(text, selectedModel);
                // 将结果中的换行符转换为HTML的<br>标签
                contentDiv.innerHTML = aiResult.replace(/\n/g, '<br>');

                // 聚焦追问输入框
                document.getElementById('followup-input').focus();
            } catch (error) {
                contentDiv.innerHTML = `<span style="color: red;">❌ 处理出错: ${error.message}</span>`;
            }
        }
    }

    // 监听选择文本事件
    document.addEventListener('mouseup', function(e) {
        const selection = window.getSelection();
        const selectedText = selection.toString().trim();

        if (selectedText.length > 0) {
            lastSelectedText = selectedText;

            // 获取选中文本的位置
            const range = selection.getRangeAt(0);
            lastSelectionRect = range.getBoundingClientRect();

            // 设置按钮位置
            button.style.left = `${lastSelectionRect.left + window.scrollX}px`;
            button.style.top = `${lastSelectionRect.bottom + window.scrollY + 5}px`;
            button.style.display = 'block';
        } else {
            // 如果没有选中文本且未点击翻译容器，隐藏按钮和容器
            if (!translationContainer.contains(e.target) &&
                e.target !== translationContainer &&
                !settingsPanel.contains(e.target) &&
                e.target !== settingsPanel) {
                button.style.display = 'none';
            }
        }
    });

    // 点击翻译按钮
    button.addEventListener('click', function() {
        if (lastSelectedText) {
            showAIResult(lastSelectedText);
            // 隐藏按钮
            button.style.display = 'none';
        }
    });

    // 点击重新生成按钮
    document.getElementById('retranslate-btn').addEventListener('click', function() {
        if (lastSelectedText) {
            const contentDiv = document.getElementById('translation-content');
            contentDiv.innerHTML = '<span class="translation-loading"></span> 正在重新处理...';

            // 清空对话历史
            conversationHistory = [];

            const modelSelector = document.getElementById('model-selector');
            processTextWithAI(lastSelectedText, modelSelector.value)
                .then(result => {
                    contentDiv.innerHTML = result.replace(/\n/g, '<br>');
                })
                .catch(error => {
                    contentDiv.innerHTML = `<span style="color: red;">❌ 处理出错: ${error.message}</span>`;
                });
        }
    });

    // 提交追问功能
    function submitFollowupQuestion() {
        const followupInput = document.getElementById('followup-input');
        const question = followupInput.value.trim();

        if (question) {
            // 禁用按钮，防止重复提交
            document.getElementById('followup-button').disabled = true;
            followupInput.disabled = true;

            // 处理追问
            showAIResult(question, true)
                .finally(() => {
                    // 重新启用输入和按钮
                    followupInput.disabled = false;
                    document.getElementById('followup-button').disabled = false;

                    // 清空输入框
                    followupInput.value = '';

                    // 聚焦输入框，方便继续输入
                    followupInput.focus();
                });
        }
    }

    // 点击追问按钮
    document.getElementById('followup-button').addEventListener('click', submitFollowupQuestion);

    // 监听追问输入框的回车键
    document.getElementById('followup-input').addEventListener('keydown', function(e) {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault(); // 防止默认的回车行为
            submitFollowupQuestion();
        }
    });

    // 模型选择变化时自动重新生成
    document.getElementById('model-selector').addEventListener('change', function() {
        const retranslateBtn = document.getElementById('retranslate-btn');
        if (retranslateBtn && translationContainer.style.display === 'block') {
            retranslateBtn.click();
        }
    });

    // 点击设置按钮
    document.getElementById('settings-btn').addEventListener('click', function() {
        // 显示设置面板
        settingsPanel.style.display = 'block';
        modalBackdrop.style.display = 'block';
    });

    // 点击关闭设置面板按钮
    document.getElementById('close-settings').addEventListener('click', function() {
        settingsPanel.style.display = 'none';
        // 如果翻译容器仍然显示，则保持模态背景，否则隐藏它
        if (translationContainer.style.display !== 'block') {
            modalBackdrop.style.display = 'none';
        }
    });

    // 点击重置提示词按钮
    document.getElementById('reset-prompt').addEventListener('click', function() {
        document.getElementById('prompt-textarea').value = DEFAULT_PROMPT;
    });

    // 点击保存设置按钮
    document.getElementById('save-settings').addEventListener('click', function() {
        const promptTextarea = document.getElementById('prompt-textarea');
        const customPrompt = promptTextarea.value.trim();

        if (customPrompt) {
            GM_setValue('custom_prompt', customPrompt);
            alert('提示词设置已保存！');
            settingsPanel.style.display = 'none';

            // 如果翻译结果容器是可见的，自动重新生成
            if (translationContainer.style.display === 'block') {
                document.getElementById('retranslate-btn').click();
            } else {
                modalBackdrop.style.display = 'none';
            }
        } else {
            alert('提示词不能为空！');
        }
    });

    // 点击关闭翻译结果按钮
    document.getElementById('translation-close').addEventListener('click', function() {
        translationContainer.style.display = 'none';
        modalBackdrop.style.display = 'none';
    });

    // 点击模态背景关闭所有面板
    modalBackdrop.addEventListener('click', function() {
        translationContainer.style.display = 'none';
        settingsPanel.style.display = 'none';
        modalBackdrop.style.display = 'none';
    });

    // 点击页面其他地方关闭按钮
    document.addEventListener('mousedown', function(e) {
        // 处理翻译按钮
        if (e.target !== button &&
            !translationContainer.contains(e.target) &&
            !settingsPanel.contains(e.target)) {
            button.style.display = 'none';
        }
    });
})();