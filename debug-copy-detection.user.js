// ==UserScript==
// @name         复制检测调试工具
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  调试复制事件检测
// <AUTHOR>
// @match        *://*/*
// @grant        none
// @run-at       document-end
// ==/UserScript==

(function() {
    'use strict';
    
    console.log('🔍 复制检测调试工具已启动');
    
    // 创建调试面板
    const debugPanel = document.createElement('div');
    debugPanel.innerHTML = `
        <div style="position: fixed; top: 10px; left: 10px; background: #000; color: #0f0; padding: 10px; border-radius: 5px; z-index: 99999; font-family: monospace; font-size: 12px; max-width: 300px;">
            <div>📋 复制检测调试</div>
            <div id="debug-log"></div>
            <button id="test-btn" style="margin-top: 5px; padding: 5px;">测试翻译框</button>
        </div>
    `;
    document.body.appendChild(debugPanel);
    
    const debugLog = document.getElementById('debug-log');
    
    function log(message) {
        console.log(message);
        debugLog.innerHTML += '<br>' + message;
        // 保持最新的10条记录
        const lines = debugLog.innerHTML.split('<br>');
        if (lines.length > 10) {
            debugLog.innerHTML = lines.slice(-10).join('<br>');
        }
    }
    
    // 方法1: copy事件
    document.addEventListener('copy', (e) => {
        log('🎯 copy事件触发');
        
        const selection = window.getSelection();
        if (selection && selection.toString()) {
            const text = selection.toString().trim();
            log('📝 选中文本: ' + text.substring(0, 30) + '...');
            showSimpleBox(text);
        }
        
        if (e.clipboardData) {
            try {
                const clipText = e.clipboardData.getData('text/plain');
                log('📋 剪贴板数据: ' + clipText.substring(0, 30) + '...');
            } catch (err) {
                log('❌ 无法读取剪贴板: ' + err.message);
            }
        }
    });
    
    // 方法2: 选择变化
    let lastSelection = '';
    document.addEventListener('selectionchange', () => {
        const selection = window.getSelection();
        if (selection && selection.toString()) {
            const text = selection.toString().trim();
            if (text !== lastSelection && text.length > 3) {
                lastSelection = text;
                log('🎯 选择变化: ' + text.substring(0, 30) + '...');
            }
        }
    });
    
    // 方法3: 键盘快捷键
    document.addEventListener('keydown', (e) => {
        if ((e.ctrlKey || e.metaKey) && e.key === 'c') {
            log('⌨️ 检测到Ctrl+C');
            setTimeout(() => {
                if (lastSelection) {
                    log('📤 准备显示: ' + lastSelection.substring(0, 30) + '...');
                    showSimpleBox(lastSelection);
                }
            }, 100);
        }
    });
    
    // 简单的翻译框
    function showSimpleBox(text) {
        // 移除已存在的框
        const existing = document.getElementById('simple-translation-box');
        if (existing) {
            existing.remove();
        }
        
        const box = document.createElement('div');
        box.id = 'simple-translation-box';
        box.innerHTML = `
            <div style="position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); 
                        width: 400px; background: white; border: 2px solid #4CAF50; 
                        border-radius: 10px; padding: 20px; z-index: 10000; 
                        box-shadow: 0 4px 20px rgba(0,0,0,0.3);">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                    <h3 style="margin: 0;">检测到复制文本</h3>
                    <button onclick="this.parentElement.parentElement.parentElement.remove()" 
                            style="background: #f44336; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer;">✕</button>
                </div>
                <div style="margin-bottom: 10px;">
                    <strong>原文:</strong>
                    <div style="background: #f5f5f5; padding: 10px; border-radius: 5px; margin-top: 5px; max-height: 100px; overflow-y: auto;">${text}</div>
                </div>
                <div style="text-align: center;">
                    <button onclick="alert('这里会调用Ollama翻译')" 
                            style="background: #4CAF50; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">
                        开始翻译
                    </button>
                </div>
            </div>
        `;
        document.body.appendChild(box);
        
        log('✅ 翻译框已显示');
        
        // 5秒后自动关闭
        setTimeout(() => {
            if (document.getElementById('simple-translation-box')) {
                document.getElementById('simple-translation-box').remove();
                log('⏰ 翻译框自动关闭');
            }
        }, 5000);
    }
    
    // 测试按钮
    document.getElementById('test-btn').addEventListener('click', () => {
        showSimpleBox('这是一个测试文本，用于验证翻译框是否正常显示。');
    });
    
    log('✅ 调试工具初始化完成');
    log('💡 请尝试复制一些文字');
    
})();
