# Ollama智能翻译助手 - 油猴脚本

一个功能强大的油猴脚本，可以在任何网页上实现智能翻译功能，通过连接本地Ollama模型提供高质量的翻译服务。

## 功能特点

- 🚀 **自动触发**: 复制文字后自动弹出翻译框
- 🤖 **本地AI**: 连接Ollama本地模型，保护隐私
- ⚙️ **可配置**: 支持自定义提示词、模型选择等
- 🎯 **智能识别**: 自动判断文本长度，避免误触发
- 🖱️ **拖拽移动**: 翻译框支持拖拽移动
- ⌨️ **快捷键**: 支持多种快捷键操作
- 📋 **一键复制**: 翻译结果一键复制到剪贴板

## 安装要求

1. **浏览器扩展**: 安装 [Tampermonkey](https://www.tampermonkey.net/) 或其他油猴脚本管理器
2. **Ollama服务**: 本地安装并运行 [Ollama](https://ollama.ai/)

## 安装步骤

### 1. 安装Ollama
```bash
# 下载并安装Ollama
curl -fsSL https://ollama.ai/install.sh | sh

# 拉取推荐的模型
ollama pull llama3.2
# 或者其他中文友好的模型
ollama pull qwen2
```

### 2. 启动Ollama服务
```bash
ollama serve
```
默认服务地址: `http://localhost:11434`

### 3. 安装油猴脚本
1. 打开Tampermonkey管理面板
2. 点击"添加新脚本"
3. 复制 `ollama-translator.user.js` 的内容并粘贴
4. 保存脚本

## 使用方法

### 基本使用
1. 在任何网页上复制文字
2. 翻译框会自动弹出并开始翻译
3. 查看翻译结果，可一键复制

### 快捷键
- `Ctrl + Shift + T`: 手动打开翻译框
- `Ctrl + Enter`: 在输入框中执行翻译
- `ESC`: 关闭翻译框或设置面板

### 手动操作
- 点击右上角的🌐图标打开翻译框
- 点击⚙️按钮打开设置面板

## 配置选项

### 基本设置
- **Ollama服务地址**: 默认 `http://localhost:11434`
- **模型名称**: 默认 `llama3.2`，可改为其他已安装的模型
- **最小文本长度**: 默认3个字符，低于此长度不会自动翻译

### 提示词设置
默认提示词：`请将以下文本翻译成中文，如果已经是中文则翻译成英文：`

你可以根据需要自定义提示词，例如：
- `请将以下内容翻译成中文，保持原文的语气和风格：`
- `作为专业翻译，请准确翻译以下文本：`
- `请将以下技术文档翻译成中文：`

### 高级设置
- **自动显示**: 是否在复制文字后自动显示翻译框
- **文本长度限制**: 避免翻译过长的文本（默认5000字符）

## 推荐模型

根据不同需求推荐以下模型：

### 中英互译
- `qwen2:7b` - 阿里千问，中文表现优秀
- `llama3.2:3b` - Meta官方，轻量快速
- `gemma2:9b` - Google出品，质量较高

### 多语言翻译
- `llama3.1:8b` - 支持多种语言
- `mistral:7b` - 欧洲语言表现好

## 故障排除

### 常见问题

1. **翻译框不出现**
   - 检查Ollama服务是否运行: `curl http://localhost:11434/api/tags`
   - 确认脚本已启用
   - 检查浏览器控制台是否有错误

2. **翻译失败**
   - 确认模型已下载: `ollama list`
   - 检查网络连接
   - 查看Ollama日志: `ollama logs`

3. **翻译质量不佳**
   - 尝试更换模型
   - 调整提示词
   - 确保模型适合当前语言对

### 调试模式
打开浏览器开发者工具(F12)，在控制台中可以看到脚本的运行日志。

## 自定义开发

脚本采用模块化设计，主要组件：
- 配置管理
- UI界面
- API调用
- 事件处理

可以根据需要修改样式、添加新功能或适配其他AI服务。

## 更新日志

### v1.0
- 基础翻译功能
- 自动复制检测
- 可配置设置
- 拖拽移动
- 快捷键支持

## 许可证

MIT License - 自由使用和修改

## 贡献

欢迎提交Issue和Pull Request来改进这个脚本！
