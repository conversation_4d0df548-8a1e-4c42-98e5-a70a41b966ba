// ==UserScript==
// @name         复制检测增强测试
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  增强版复制检测测试，包含多种检测方法
// <AUTHOR>
// @match        *://*/*
// @grant        GM_setValue
// @grant        GM_getValue
// @run-at       document-end
// ==/UserScript==

(function() {
    'use strict';
    
    console.log('🔍 复制检测增强测试启动');
    
    // 创建状态面板
    const statusPanel = document.createElement('div');
    statusPanel.innerHTML = `
        <div id="copy-status" style="
            position: fixed; top: 10px; right: 10px; width: 300px;
            background: rgba(0,0,0,0.8); color: white; padding: 10px;
            border-radius: 5px; z-index: 99999; font-family: monospace;
            font-size: 12px; max-height: 400px; overflow-y: auto;
        ">
            <div style="font-weight: bold; margin-bottom: 10px;">📋 复制检测状态</div>
            <div>自动显示: <span id="auto-status">启用</span></div>
            <div>最后复制: <span id="last-copy">无</span></div>
            <div>检测次数: <span id="copy-count">0</span></div>
            <hr style="margin: 10px 0;">
            <div id="event-log" style="max-height: 200px; overflow-y: auto;"></div>
            <hr style="margin: 10px 0;">
            <button id="toggle-auto" style="margin-right: 5px; padding: 3px 6px;">切换自动</button>
            <button id="manual-test" style="margin-right: 5px; padding: 3px 6px;">手动测试</button>
            <button id="clear-log" style="padding: 3px 6px;">清空日志</button>
        </div>
    `;
    document.body.appendChild(statusPanel);
    
    // 状态变量
    let autoShow = GM_getValue('autoShow', true);
    let copyCount = 0;
    let lastCopiedText = '';
    let eventLog = [];
    
    // 更新状态显示
    function updateStatus() {
        document.getElementById('auto-status').textContent = autoShow ? '启用' : '禁用';
        document.getElementById('copy-count').textContent = copyCount;
        document.getElementById('last-copy').textContent = lastCopiedText ? 
            lastCopiedText.substring(0, 20) + '...' : '无';
    }
    
    // 添加日志
    function addLog(message, type = 'info') {
        const timestamp = new Date().toLocaleTimeString();
        const logEntry = `[${timestamp}] ${message}`;
        eventLog.unshift(logEntry);
        
        // 保持最新20条记录
        if (eventLog.length > 20) {
            eventLog = eventLog.slice(0, 20);
        }
        
        const logDiv = document.getElementById('event-log');
        logDiv.innerHTML = eventLog.map(entry => 
            `<div style="margin-bottom: 2px; color: ${type === 'error' ? '#ff6b6b' : 
                                                    type === 'success' ? '#51cf66' : '#74c0fc'}">${entry}</div>`
        ).join('');
        
        console.log(logEntry);
    }
    
    // 显示翻译框
    function showTranslationBox(text) {
        addLog(`显示翻译框: ${text.substring(0, 30)}...`, 'success');
        
        // 移除已存在的框
        const existing = document.getElementById('test-translation-box');
        if (existing) existing.remove();
        
        const box = document.createElement('div');
        box.id = 'test-translation-box';
        box.innerHTML = `
            <div style="
                position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%);
                width: 450px; max-width: 90vw; background: white; border: 2px solid #4CAF50;
                border-radius: 10px; padding: 20px; z-index: 10001;
                box-shadow: 0 4px 20px rgba(0,0,0,0.3); font-family: Arial, sans-serif;
            ">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                    <h3 style="margin: 0; color: #4CAF50;">🌐 检测到复制文本</h3>
                    <button onclick="this.parentElement.parentElement.parentElement.remove()" 
                            style="background: #f44336; color: white; border: none; padding: 5px 10px; 
                                   border-radius: 3px; cursor: pointer;">✕</button>
                </div>
                <div style="margin-bottom: 15px;">
                    <strong>原文:</strong>
                    <div style="background: #f5f5f5; padding: 10px; border-radius: 5px; margin-top: 5px; 
                               max-height: 150px; overflow-y: auto; word-wrap: break-word;">${text}</div>
                </div>
                <div style="margin-bottom: 15px;">
                    <strong>译文:</strong>
                    <div id="translation-output" style="background: #e8f5e8; padding: 10px; border-radius: 5px; 
                                                       margin-top: 5px; min-height: 50px;">
                        点击下方按钮开始翻译...
                    </div>
                </div>
                <div style="text-align: center;">
                    <button onclick="startTranslation('${text.replace(/'/g, "\\'")}', this)" 
                            style="background: #4CAF50; color: white; border: none; padding: 10px 20px; 
                                   border-radius: 5px; cursor: pointer; margin-right: 10px;">
                        🚀 开始翻译
                    </button>
                    <button onclick="this.parentElement.parentElement.parentElement.remove()" 
                            style="background: #666; color: white; border: none; padding: 10px 20px; 
                                   border-radius: 5px; cursor: pointer;">
                        关闭
                    </button>
                </div>
            </div>
        `;
        document.body.appendChild(box);
        
        // 5秒后自动关闭提示
        setTimeout(() => {
            const autoCloseDiv = document.createElement('div');
            autoCloseDiv.style.cssText = 'color: #666; font-size: 12px; text-align: center; margin-top: 10px;';
            autoCloseDiv.textContent = '(将在10秒后自动关闭)';
            box.querySelector('div').appendChild(autoCloseDiv);
        }, 5000);
        
        // 15秒后自动关闭
        setTimeout(() => {
            if (document.getElementById('test-translation-box')) {
                document.getElementById('test-translation-box').remove();
                addLog('翻译框自动关闭');
            }
        }, 15000);
    }
    
    // 全局函数供按钮调用
    window.startTranslation = function(text, button) {
        const output = document.getElementById('translation-output');
        output.textContent = '这里会调用Ollama进行翻译...';
        button.textContent = '翻译完成';
        button.disabled = true;
        addLog('模拟翻译完成');
    };
    
    // 方法1: copy事件监听
    document.addEventListener('copy', (e) => {
        copyCount++;
        addLog(`copy事件触发 (第${copyCount}次)`);
        
        if (!autoShow) {
            addLog('自动显示已禁用，跳过处理');
            updateStatus();
            return;
        }
        
        // 获取选中文本
        const selection = window.getSelection();
        let copiedText = '';
        
        if (selection && selection.toString()) {
            copiedText = selection.toString().trim();
            addLog(`从selection获取: ${copiedText.substring(0, 30)}...`);
        }
        
        // 尝试从clipboardData获取
        if (!copiedText && e.clipboardData) {
            try {
                const clipData = e.clipboardData.getData('text/plain');
                if (clipData) {
                    copiedText = clipData.trim();
                    addLog(`从clipboardData获取: ${copiedText.substring(0, 30)}...`);
                }
            } catch (err) {
                addLog(`clipboardData访问失败: ${err.message}`, 'error');
            }
        }
        
        if (copiedText && copiedText.length >= 3 && copiedText !== lastCopiedText) {
            lastCopiedText = copiedText;
            addLog(`准备显示翻译框，延迟300ms`, 'success');
            
            setTimeout(() => {
                showTranslationBox(copiedText);
            }, 300);
        } else {
            addLog(`文本不符合条件: 长度=${copiedText.length}, 重复=${copiedText === lastCopiedText}`, 'error');
        }
        
        updateStatus();
    });
    
    // 方法2: 选择变化监听
    let lastSelection = '';
    document.addEventListener('selectionchange', () => {
        const selection = window.getSelection();
        if (selection && selection.toString()) {
            const currentSelection = selection.toString().trim();
            if (currentSelection !== lastSelection && currentSelection.length > 3) {
                lastSelection = currentSelection;
                addLog(`选择变化: ${currentSelection.substring(0, 20)}...`);
            }
        }
    });
    
    // 方法3: 键盘快捷键监听
    document.addEventListener('keydown', (e) => {
        if ((e.ctrlKey || e.metaKey) && e.key === 'c') {
            addLog('检测到Ctrl+C快捷键');
            
            setTimeout(() => {
                if (lastSelection && autoShow) {
                    addLog(`通过快捷键触发: ${lastSelection.substring(0, 20)}...`);
                    if (lastSelection !== lastCopiedText) {
                        lastCopiedText = lastSelection;
                        showTranslationBox(lastSelection);
                    }
                }
            }, 100);
        }
        
        // Ctrl+Shift+T 手动打开
        if (e.ctrlKey && e.shiftKey && e.key === 'T') {
            e.preventDefault();
            addLog('手动快捷键触发');
            showTranslationBox('这是通过快捷键Ctrl+Shift+T手动触发的测试文本');
        }
    });
    
    // 按钮事件
    document.getElementById('toggle-auto').addEventListener('click', () => {
        autoShow = !autoShow;
        GM_setValue('autoShow', autoShow);
        updateStatus();
        addLog(`自动显示已${autoShow ? '启用' : '禁用'}`);
    });
    
    document.getElementById('manual-test').addEventListener('click', () => {
        addLog('手动测试触发');
        showTranslationBox('这是手动测试的示例文本，用于验证翻译框是否正常工作。');
    });
    
    document.getElementById('clear-log').addEventListener('click', () => {
        eventLog = [];
        document.getElementById('event-log').innerHTML = '';
        addLog('日志已清空');
    });
    
    // 初始化
    updateStatus();
    addLog('复制检测增强测试已启动');
    addLog('请尝试复制一些文字，或点击"手动测试"按钮');
    
})();
