// ==UserScript==
// @name         Ollama智能翻译助手-最终版
// @namespace    http://tampermonkey.net/
// @version      2.0
// @description  复制文字后自动弹出翻译框，连接ollama本地模型（基于测试成功的方法）
// <AUTHOR>
// @match        *://*/*
// @grant        GM_setValue
// @grant        GM_getValue
// @grant        GM_xmlhttpRequest
// @run-at       document-end
// ==/UserScript==

(function() {
    'use strict';

    // 配置管理
    const config = {
        ollamaUrl: GM_getValue('ollamaUrl', 'http://localhost:11434'),
        model: GM_getValue('model', 'qwen3:8b'),
        prompt: GM_getValue('prompt', '请将以下文本翻译成中文，如果已经是中文则翻译成英文：'),
        autoShow: GM_getValue('autoShow', true),
        minTextLength: GM_getValue('minTextLength', 3)
    };

    function saveConfig() {
        GM_setValue('ollamaUrl', config.ollamaUrl);
        GM_setValue('model', config.model);
        GM_setValue('prompt', config.prompt);
        GM_setValue('autoShow', config.autoShow);
        GM_setValue('minTextLength', config.minTextLength);
    }

    let lastCopiedText = '';
    let lastSelection = '';
    let isProcessing = false;

    // 调用Ollama API
    async function callOllama(text) {
        return new Promise((resolve, reject) => {
            const requestData = {
                model: config.model,
                prompt: config.prompt + '\n\n' + text,
                stream: false
            };

            GM_xmlhttpRequest({
                method: 'POST',
                url: config.ollamaUrl + '/api/generate',
                headers: { 'Content-Type': 'application/json' },
                data: JSON.stringify(requestData),
                timeout: 30000,
                onload: function(response) {
                    try {
                        const result = JSON.parse(response.responseText);
                        if (result.response) {
                            resolve(result.response.trim());
                        } else {
                            reject('API返回格式错误');
                        }
                    } catch (e) {
                        reject('解析响应失败: ' + e.message);
                    }
                },
                onerror: () => reject('请求失败，请检查Ollama服务是否运行在 ' + config.ollamaUrl),
                ontimeout: () => reject('请求超时，请检查网络连接')
            });
        });
    }

    // 执行翻译
    async function performTranslation(text, outputElement, buttonElement) {
        if (isProcessing) return;
        
        isProcessing = true;
        outputElement.textContent = '🔄 翻译中，请稍候...';
        buttonElement.textContent = '翻译中...';
        buttonElement.disabled = true;
        
        try {
            const translation = await callOllama(text);
            outputElement.textContent = translation;
            buttonElement.textContent = '✅ 翻译完成';
            
            // 添加复制按钮
            const copyBtn = document.createElement('button');
            copyBtn.textContent = '📋 复制译文';
            copyBtn.style.cssText = `
                margin-left: 10px; padding: 8px 16px; background: #2196F3; color: white;
                border: none; border-radius: 4px; cursor: pointer; font-size: 14px;
            `;
            copyBtn.onclick = () => {
                navigator.clipboard.writeText(translation).then(() => {
                    copyBtn.textContent = '✅ 已复制';
                    setTimeout(() => copyBtn.textContent = '📋 复制译文', 2000);
                });
            };
            buttonElement.parentNode.appendChild(copyBtn);
            
        } catch (error) {
            outputElement.textContent = '❌ 翻译失败: ' + error;
            buttonElement.textContent = '🔄 重试翻译';
        } finally {
            isProcessing = false;
            buttonElement.disabled = false;
        }
    }

    // 显示翻译框
    function showTranslationBox(text) {
        console.log('🌐 显示翻译框:', text.substring(0, 50) + '...');
        
        // 移除已存在的翻译框
        const existing = document.getElementById('ollama-translation-box');
        if (existing) existing.remove();
        
        const box = document.createElement('div');
        box.id = 'ollama-translation-box';
        box.innerHTML = `
            <div style="
                position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%);
                width: 550px; max-width: 90vw; max-height: 80vh; background: white;
                border: 2px solid #4CAF50; border-radius: 12px; z-index: 10000;
                box-shadow: 0 8px 32px rgba(0,0,0,0.3); font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                overflow: hidden;
            ">
                <!-- 标题栏 -->
                <div style="
                    background: linear-gradient(135deg, #4CAF50, #45a049); color: white; padding: 15px 20px;
                    display: flex; justify-content: space-between; align-items: center; cursor: move;
                " id="translation-header">
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <span style="font-size: 18px;">🌐</span>
                        <span style="font-weight: 600;">Ollama智能翻译</span>
                    </div>
                    <div>
                        <button id="settings-btn" style="
                            background: rgba(255,255,255,0.2); border: none; color: white; padding: 6px 10px;
                            border-radius: 6px; cursor: pointer; margin-right: 8px; font-size: 14px;
                        " title="设置">⚙️</button>
                        <button id="close-btn" style="
                            background: rgba(255,255,255,0.2); border: none; color: white; padding: 6px 10px;
                            border-radius: 6px; cursor: pointer; font-size: 14px;
                        " title="关闭">✕</button>
                    </div>
                </div>
                
                <!-- 内容区域 -->
                <div style="padding: 20px; max-height: calc(80vh - 80px); overflow-y: auto;">
                    <!-- 原文区域 -->
                    <div style="margin-bottom: 20px;">
                        <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #333; font-size: 14px;">
                            📝 原文
                        </label>
                        <div style="
                            background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 8px; padding: 12px;
                            max-height: 120px; overflow-y: auto; font-size: 14px; line-height: 1.5;
                            word-wrap: break-word; white-space: pre-wrap;
                        ">${text}</div>
                    </div>
                    
                    <!-- 译文区域 -->
                    <div style="margin-bottom: 20px;">
                        <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #333; font-size: 14px;">
                            🎯 译文
                        </label>
                        <div id="translation-output" style="
                            background: #f0f8f0; border: 1px solid #c8e6c9; border-radius: 8px; padding: 12px;
                            min-height: 80px; max-height: 200px; overflow-y: auto; font-size: 14px; line-height: 1.5;
                            word-wrap: break-word; white-space: pre-wrap; color: #2e7d32;
                        ">⏳ 准备翻译...</div>
                    </div>
                    
                    <!-- 按钮区域 -->
                    <div style="display: flex; gap: 12px; justify-content: center; align-items: center;">
                        <button id="translate-btn" style="
                            background: linear-gradient(135deg, #4CAF50, #45a049); color: white; border: none;
                            padding: 12px 24px; border-radius: 8px; cursor: pointer; font-size: 14px; font-weight: 600;
                            box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3); transition: all 0.2s;
                        ">🚀 开始翻译</button>
                        <button id="new-translate-btn" style="
                            background: #6c757d; color: white; border: none; padding: 12px 24px;
                            border-radius: 8px; cursor: pointer; font-size: 14px; font-weight: 600;
                        ">📝 新翻译</button>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(box);
        
        // 设置事件监听器
        setupTranslationBoxEvents(text);
        
        // 自动开始翻译
        if (text.length >= config.minTextLength) {
            setTimeout(() => {
                const translateBtn = document.getElementById('translate-btn');
                const outputDiv = document.getElementById('translation-output');
                if (translateBtn && outputDiv) {
                    performTranslation(text, outputDiv, translateBtn);
                }
            }, 500);
        }
    }

    // 设置翻译框事件
    function setupTranslationBoxEvents(text) {
        // 关闭按钮
        document.getElementById('close-btn').onclick = () => {
            document.getElementById('ollama-translation-box').remove();
        };
        
        // 翻译按钮
        document.getElementById('translate-btn').onclick = () => {
            const outputDiv = document.getElementById('translation-output');
            const translateBtn = document.getElementById('translate-btn');
            performTranslation(text, outputDiv, translateBtn);
        };
        
        // 新翻译按钮
        document.getElementById('new-translate-btn').onclick = () => {
            document.getElementById('ollama-translation-box').remove();
            showTranslationBox('');
        };
        
        // 设置按钮
        document.getElementById('settings-btn').onclick = showSettings;
        
        // 拖拽功能
        let isDragging = false;
        const header = document.getElementById('translation-header');
        const box = document.getElementById('ollama-translation-box');
        
        header.onmousedown = (e) => {
            isDragging = true;
            const rect = box.getBoundingClientRect();
            const offsetX = e.clientX - rect.left;
            const offsetY = e.clientY - rect.top;
            
            const onMouseMove = (e) => {
                if (isDragging) {
                    box.style.left = (e.clientX - offsetX) + 'px';
                    box.style.top = (e.clientY - offsetY) + 'px';
                    box.style.transform = 'none';
                }
            };
            
            const onMouseUp = () => {
                isDragging = false;
                document.removeEventListener('mousemove', onMouseMove);
                document.removeEventListener('mouseup', onMouseUp);
            };
            
            document.addEventListener('mousemove', onMouseMove);
            document.addEventListener('mouseup', onMouseUp);
        };
    }

    // 设置面板
    function showSettings() {
        const newUrl = prompt('Ollama服务地址:', config.ollamaUrl);
        if (newUrl !== null) {
            config.ollamaUrl = newUrl.trim();
        }
        
        const newModel = prompt('模型名称:', config.model);
        if (newModel !== null) {
            config.model = newModel.trim();
        }
        
        const newPrompt = prompt('提示词:', config.prompt);
        if (newPrompt !== null) {
            config.prompt = newPrompt.trim();
        }
        
        const autoShowChoice = confirm('是否启用复制自动翻译？\n点击"确定"启用，点击"取消"禁用');
        config.autoShow = autoShowChoice;
        
        saveConfig();
        alert('设置已保存！');
    }

    // 复制检测 - 使用成功的方法
    document.addEventListener('selectionchange', () => {
        if (!config.autoShow) return;
        
        const selection = window.getSelection();
        if (selection && selection.toString()) {
            const currentSelection = selection.toString().trim();
            if (currentSelection !== lastSelection && 
                currentSelection.length >= config.minTextLength &&
                currentSelection.length < 5000) {
                lastSelection = currentSelection;
            }
        }
    });

    // 键盘事件监听
    document.addEventListener('keydown', (e) => {
        // 复制快捷键检测
        if ((e.ctrlKey || e.metaKey) && e.key === 'c' && config.autoShow) {
            setTimeout(() => {
                if (lastSelection && 
                    lastSelection !== lastCopiedText && 
                    lastSelection.length >= config.minTextLength) {
                    lastCopiedText = lastSelection;
                    console.log('🎯 检测到复制操作:', lastSelection.substring(0, 30) + '...');
                    showTranslationBox(lastSelection);
                }
            }, 200);
        }
        
        // 手动快捷键 Ctrl+Shift+T
        if (e.ctrlKey && e.shiftKey && e.key === 'T') {
            e.preventDefault();
            showTranslationBox('请在此输入要翻译的文本...');
        }
        
        // ESC关闭
        if (e.key === 'Escape') {
            const box = document.getElementById('ollama-translation-box');
            if (box) box.remove();
        }
    });

    // 初始化
    console.log('🌐 Ollama智能翻译助手已启动');
    console.log('📋 复制自动翻译:', config.autoShow ? '已启用' : '已禁用');
    console.log('⌨️ 快捷键: Ctrl+Shift+T 手动打开');
    console.log('⚙️ 当前配置:', config);

})();
