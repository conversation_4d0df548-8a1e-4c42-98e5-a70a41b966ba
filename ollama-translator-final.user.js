// ==UserScript==
// @name         Ollama智能翻译助手-最终版
// @namespace    http://tampermonkey.net/
// @version      2.0
// @description  复制文字后自动弹出翻译框，连接ollama本地模型（基于测试成功的方法）
// <AUTHOR>
// @match        *://*/*
// @grant        GM_setValue
// @grant        GM_getValue
// @grant        GM_xmlhttpRequest
// @run-at       document-end
// ==/UserScript==

(function() {
    'use strict';

    // 配置管理
    const config = {
        ollamaUrl: GM_getValue('ollamaUrl', 'http://localhost:11434'),
        model: GM_getValue('model', 'qwen3:8b'),
        prompt: GM_getValue('prompt', '请将以下文本翻译成中文，如果已经是中文则翻译成英文：'),
        autoShow: GM_getValue('autoShow', true),
        minTextLength: GM_getValue('minTextLength', 3)
    };

    function saveConfig() {
        GM_setValue('ollamaUrl', config.ollamaUrl);
        GM_setValue('model', config.model);
        GM_setValue('prompt', config.prompt);
        GM_setValue('autoShow', config.autoShow);
        GM_setValue('minTextLength', config.minTextLength);
    }

    let lastCopiedText = '';
    let lastSelection = '';
    let isProcessing = false;

    // 调用Ollama API
    async function callOllama(text) {
        return new Promise((resolve, reject) => {
            const requestData = {
                model: config.model,
                prompt: config.prompt + '\n\n' + text,
                stream: false
            };

            GM_xmlhttpRequest({
                method: 'POST',
                url: config.ollamaUrl + '/api/generate',
                headers: { 'Content-Type': 'application/json' },
                data: JSON.stringify(requestData),
                timeout: 30000,
                onload: function(response) {
                    try {
                        const result = JSON.parse(response.responseText);
                        if (result.response) {
                            resolve(result.response.trim());
                        } else {
                            reject('API返回格式错误');
                        }
                    } catch (e) {
                        reject('解析响应失败: ' + e.message);
                    }
                },
                onerror: () => reject('请求失败，请检查Ollama服务是否运行在 ' + config.ollamaUrl),
                ontimeout: () => reject('请求超时，请检查网络连接')
            });
        });
    }

    // 执行翻译
    async function performTranslation(text, outputElement, buttonElement) {
        if (isProcessing) return;
        
        isProcessing = true;
        outputElement.textContent = '🔄 翻译中，请稍候...';
        buttonElement.textContent = '翻译中...';
        buttonElement.disabled = true;
        
        try {
            const translation = await callOllama(text);
            outputElement.textContent = translation;
            buttonElement.textContent = '✅ 翻译完成';
            
            // 添加复制按钮
            const copyBtn = document.createElement('button');
            copyBtn.textContent = '📋 复制译文';
            copyBtn.style.cssText = `
                margin-left: 10px; padding: 8px 16px; background: #2196F3; color: white;
                border: none; border-radius: 4px; cursor: pointer; font-size: 14px;
            `;
            copyBtn.onclick = () => {
                navigator.clipboard.writeText(translation).then(() => {
                    copyBtn.textContent = '✅ 已复制';
                    setTimeout(() => copyBtn.textContent = '📋 复制译文', 2000);
                });
            };
            buttonElement.parentNode.appendChild(copyBtn);
            
        } catch (error) {
            outputElement.textContent = '❌ 翻译失败: ' + error;
            buttonElement.textContent = '🔄 重试翻译';
        } finally {
            isProcessing = false;
            buttonElement.disabled = false;
        }
    }

    // 显示翻译框
    function showTranslationBox(text) {
        console.log('🌐 显示翻译框:', text.substring(0, 50) + '...');
        
        // 移除已存在的翻译框
        const existing = document.getElementById('ollama-translation-box');
        if (existing) existing.remove();
        
        const box = document.createElement('div');
        box.id = 'ollama-translation-box';
        box.innerHTML = `
            <div style="
                position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%);
                width: 550px; max-width: 90vw; max-height: 80vh; background: white;
                border: 2px solid #4CAF50; border-radius: 12px; z-index: 10000;
                box-shadow: 0 8px 32px rgba(0,0,0,0.3); font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                overflow: hidden;
            ">
                <!-- 标题栏 -->
                <div style="
                    background: linear-gradient(135deg, #4CAF50, #45a049); color: white; padding: 15px 20px;
                    display: flex; justify-content: space-between; align-items: center; cursor: move;
                " id="translation-header">
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <span style="font-size: 18px;">🌐</span>
                        <span style="font-weight: 600;">Ollama智能翻译</span>
                    </div>
                    <div>
                        <button id="settings-btn" style="
                            background: rgba(255,255,255,0.2); border: none; color: white; padding: 6px 10px;
                            border-radius: 6px; cursor: pointer; margin-right: 8px; font-size: 14px;
                        " title="设置">⚙️</button>
                        <button id="close-btn" style="
                            background: rgba(255,255,255,0.2); border: none; color: white; padding: 6px 10px;
                            border-radius: 6px; cursor: pointer; font-size: 14px;
                        " title="关闭">✕</button>
                    </div>
                </div>
                
                <!-- 内容区域 -->
                <div style="padding: 20px; max-height: calc(80vh - 80px); overflow-y: auto;">
                    <!-- 原文区域 -->
                    <div style="margin-bottom: 20px;">
                        <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #333; font-size: 14px;">
                            📝 原文
                        </label>
                        <div style="
                            background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 8px; padding: 12px;
                            max-height: 120px; overflow-y: auto; font-size: 14px; line-height: 1.5;
                            word-wrap: break-word; white-space: pre-wrap;
                        ">${text}</div>
                    </div>
                    
                    <!-- 译文区域 -->
                    <div style="margin-bottom: 20px;">
                        <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #333; font-size: 14px;">
                            🎯 译文
                        </label>
                        <div id="translation-output" style="
                            background: #f0f8f0; border: 1px solid #c8e6c9; border-radius: 8px; padding: 12px;
                            min-height: 80px; max-height: 200px; overflow-y: auto; font-size: 14px; line-height: 1.5;
                            word-wrap: break-word; white-space: pre-wrap; color: #2e7d32;
                        ">⏳ 准备翻译...</div>
                    </div>
                    
                    <!-- 按钮区域 -->
                    <div style="display: flex; gap: 12px; justify-content: center; align-items: center;">
                        <button id="translate-btn" style="
                            background: linear-gradient(135deg, #4CAF50, #45a049); color: white; border: none;
                            padding: 12px 24px; border-radius: 8px; cursor: pointer; font-size: 14px; font-weight: 600;
                            box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3); transition: all 0.2s;
                        ">🚀 开始翻译</button>
                        <button id="new-translate-btn" style="
                            background: #6c757d; color: white; border: none; padding: 12px 24px;
                            border-radius: 8px; cursor: pointer; font-size: 14px; font-weight: 600;
                        ">📝 新翻译</button>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(box);
        
        // 设置事件监听器
        setupTranslationBoxEvents(text);
        
        // 自动开始翻译
        if (text.length >= config.minTextLength) {
            setTimeout(() => {
                const translateBtn = document.getElementById('translate-btn');
                const outputDiv = document.getElementById('translation-output');
                if (translateBtn && outputDiv) {
                    performTranslation(text, outputDiv, translateBtn);
                }
            }, 500);
        }
    }

    // 设置翻译框事件
    function setupTranslationBoxEvents(text) {
        // 关闭按钮
        document.getElementById('close-btn').onclick = () => {
            document.getElementById('ollama-translation-box').remove();
        };
        
        // 翻译按钮
        document.getElementById('translate-btn').onclick = () => {
            const outputDiv = document.getElementById('translation-output');
            const translateBtn = document.getElementById('translate-btn');
            performTranslation(text, outputDiv, translateBtn);
        };
        
        // 新翻译按钮
        document.getElementById('new-translate-btn').onclick = () => {
            document.getElementById('ollama-translation-box').remove();
            showTranslationBox('');
        };
        
        // 设置按钮
        document.getElementById('settings-btn').onclick = (e) => {
            e.preventDefault();
            e.stopPropagation();
            console.log('🔧 设置按钮被点击');
            showSettings();
        };
        
        // 拖拽功能
        let isDragging = false;
        const header = document.getElementById('translation-header');
        const box = document.getElementById('ollama-translation-box');
        
        header.onmousedown = (e) => {
            isDragging = true;
            const rect = box.getBoundingClientRect();
            const offsetX = e.clientX - rect.left;
            const offsetY = e.clientY - rect.top;
            
            const onMouseMove = (e) => {
                if (isDragging) {
                    box.style.left = (e.clientX - offsetX) + 'px';
                    box.style.top = (e.clientY - offsetY) + 'px';
                    box.style.transform = 'none';
                }
            };
            
            const onMouseUp = () => {
                isDragging = false;
                document.removeEventListener('mousemove', onMouseMove);
                document.removeEventListener('mouseup', onMouseUp);
            };
            
            document.addEventListener('mousemove', onMouseMove);
            document.addEventListener('mouseup', onMouseUp);
        };
    }

    // 设置面板
    function showSettings() {
        console.log('🔧 打开设置面板');

        // 移除已存在的设置面板
        const existing = document.getElementById('ollama-settings-panel');
        if (existing) existing.remove();

        const settingsPanel = document.createElement('div');
        settingsPanel.id = 'ollama-settings-panel';
        settingsPanel.innerHTML = `
            <div style="
                position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%);
                width: 500px; max-width: 90vw; background: white; border: 2px solid #2196F3;
                border-radius: 12px; z-index: 10001; box-shadow: 0 8px 32px rgba(0,0,0,0.4);
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            ">
                <!-- 标题栏 -->
                <div style="
                    background: linear-gradient(135deg, #2196F3, #1976D2); color: white; padding: 15px 20px;
                    display: flex; justify-content: space-between; align-items: center; border-radius: 10px 10px 0 0;
                ">
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <span style="font-size: 18px;">⚙️</span>
                        <span style="font-weight: 600;">翻译设置</span>
                    </div>
                    <button id="settings-close-btn" style="
                        background: rgba(255,255,255,0.2); border: none; color: white; padding: 6px 10px;
                        border-radius: 6px; cursor: pointer; font-size: 14px;
                    ">✕</button>
                </div>

                <!-- 设置内容 -->
                <div style="padding: 20px;">
                    <div style="margin-bottom: 20px;">
                        <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #333;">
                            🌐 Ollama服务地址
                        </label>
                        <input type="text" id="ollama-url" value="${config.ollamaUrl}" style="
                            width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px;
                            font-size: 14px; box-sizing: border-box;
                        ">
                    </div>

                    <div style="margin-bottom: 20px;">
                        <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #333;">
                            🤖 模型名称
                        </label>
                        <input type="text" id="model-name" value="${config.model}" style="
                            width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px;
                            font-size: 14px; box-sizing: border-box;
                        ">
                        <small style="color: #666; font-size: 12px;">
                            常用模型: qwen3:8b, qwen2.5:7b, llama3.2, gemma2:9b
                        </small>
                    </div>

                    <div style="margin-bottom: 20px;">
                        <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #333;">
                            💬 翻译提示词
                        </label>
                        <textarea id="prompt-text" style="
                            width: 100%; height: 80px; padding: 10px; border: 1px solid #ddd; border-radius: 6px;
                            font-size: 14px; box-sizing: border-box; resize: vertical;
                        ">${config.prompt}</textarea>
                    </div>

                    <div style="margin-bottom: 20px;">
                        <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
                            <input type="checkbox" id="auto-show" ${config.autoShow ? 'checked' : ''} style="
                                width: 18px; height: 18px; cursor: pointer;
                            ">
                            <span style="font-weight: 600; color: #333;">📋 复制文字后自动显示翻译框</span>
                        </label>
                    </div>

                    <div style="margin-bottom: 20px;">
                        <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #333;">
                            📏 最小文本长度
                        </label>
                        <input type="number" id="min-length" value="${config.minTextLength}" min="1" max="100" style="
                            width: 100px; padding: 8px; border: 1px solid #ddd; border-radius: 6px;
                            font-size: 14px;
                        ">
                        <small style="color: #666; font-size: 12px; margin-left: 10px;">
                            字符数少于此值不会自动翻译
                        </small>
                    </div>

                    <!-- 按钮区域 -->
                    <div style="display: flex; gap: 12px; justify-content: center; padding-top: 10px; border-top: 1px solid #eee;">
                        <button id="save-settings-btn" style="
                            background: linear-gradient(135deg, #4CAF50, #45a049); color: white; border: none;
                            padding: 12px 24px; border-radius: 8px; cursor: pointer; font-size: 14px; font-weight: 600;
                        ">💾 保存设置</button>
                        <button id="test-connection-btn" style="
                            background: #FF9800; color: white; border: none; padding: 12px 24px;
                            border-radius: 8px; cursor: pointer; font-size: 14px; font-weight: 600;
                        ">🔗 测试连接</button>
                        <button id="cancel-settings-btn" style="
                            background: #6c757d; color: white; border: none; padding: 12px 24px;
                            border-radius: 8px; cursor: pointer; font-size: 14px; font-weight: 600;
                        ">❌ 取消</button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(settingsPanel);

        // 设置事件监听器
        setupSettingsEvents();
    }

    // 设置面板事件处理
    function setupSettingsEvents() {
        // 关闭按钮
        document.getElementById('settings-close-btn').onclick = () => {
            document.getElementById('ollama-settings-panel').remove();
        };

        // 取消按钮
        document.getElementById('cancel-settings-btn').onclick = () => {
            document.getElementById('ollama-settings-panel').remove();
        };

        // 保存设置按钮
        document.getElementById('save-settings-btn').onclick = () => {
            const newUrl = document.getElementById('ollama-url').value.trim();
            const newModel = document.getElementById('model-name').value.trim();
            const newPrompt = document.getElementById('prompt-text').value.trim();
            const newAutoShow = document.getElementById('auto-show').checked;
            const newMinLength = parseInt(document.getElementById('min-length').value) || 3;

            // 验证输入
            if (!newUrl) {
                alert('❌ 请输入Ollama服务地址！');
                return;
            }
            if (!newModel) {
                alert('❌ 请输入模型名称！');
                return;
            }
            if (!newPrompt) {
                alert('❌ 请输入提示词！');
                return;
            }

            // 保存配置
            config.ollamaUrl = newUrl;
            config.model = newModel;
            config.prompt = newPrompt;
            config.autoShow = newAutoShow;
            config.minTextLength = newMinLength;

            saveConfig();

            // 显示保存成功
            const saveBtn = document.getElementById('save-settings-btn');
            const originalText = saveBtn.textContent;
            saveBtn.textContent = '✅ 已保存';
            saveBtn.style.background = '#4CAF50';

            setTimeout(() => {
                saveBtn.textContent = originalText;
                saveBtn.style.background = 'linear-gradient(135deg, #4CAF50, #45a049)';
                document.getElementById('ollama-settings-panel').remove();
            }, 1500);

            console.log('⚙️ 设置已保存:', config);
        };

        // 测试连接按钮
        document.getElementById('test-connection-btn').onclick = async () => {
            const testBtn = document.getElementById('test-connection-btn');
            const originalText = testBtn.textContent;
            testBtn.textContent = '🔄 测试中...';
            testBtn.disabled = true;

            try {
                const testUrl = document.getElementById('ollama-url').value.trim();
                const testModel = document.getElementById('model-name').value.trim();

                const response = await fetch(testUrl + '/api/generate', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        model: testModel,
                        prompt: 'Hello',
                        stream: false
                    })
                });

                if (response.ok) {
                    testBtn.textContent = '✅ 连接成功';
                    testBtn.style.background = '#4CAF50';
                } else {
                    testBtn.textContent = '❌ 连接失败';
                    testBtn.style.background = '#f44336';
                }
            } catch (error) {
                testBtn.textContent = '❌ 连接失败';
                testBtn.style.background = '#f44336';
                console.error('连接测试失败:', error);
            } finally {
                testBtn.disabled = false;
                setTimeout(() => {
                    testBtn.textContent = originalText;
                    testBtn.style.background = '#FF9800';
                }, 2000);
            }
        };

        // ESC键关闭
        const escHandler = (e) => {
            if (e.key === 'Escape') {
                const panel = document.getElementById('ollama-settings-panel');
                if (panel) {
                    panel.remove();
                    document.removeEventListener('keydown', escHandler);
                }
            }
        };
        document.addEventListener('keydown', escHandler);
    }

    // 复制检测 - 使用成功的方法
    document.addEventListener('selectionchange', () => {
        if (!config.autoShow) return;
        
        const selection = window.getSelection();
        if (selection && selection.toString()) {
            const currentSelection = selection.toString().trim();
            if (currentSelection !== lastSelection && 
                currentSelection.length >= config.minTextLength &&
                currentSelection.length < 5000) {
                lastSelection = currentSelection;
            }
        }
    });

    // 键盘事件监听
    document.addEventListener('keydown', (e) => {
        // 复制快捷键检测
        if ((e.ctrlKey || e.metaKey) && e.key === 'c' && config.autoShow) {
            setTimeout(() => {
                if (lastSelection && 
                    lastSelection !== lastCopiedText && 
                    lastSelection.length >= config.minTextLength) {
                    lastCopiedText = lastSelection;
                    console.log('🎯 检测到复制操作:', lastSelection.substring(0, 30) + '...');
                    showTranslationBox(lastSelection);
                }
            }, 200);
        }
        
        // 手动快捷键 Ctrl+Shift+T
        if (e.ctrlKey && e.shiftKey && e.key === 'T') {
            e.preventDefault();
            showTranslationBox('请在此输入要翻译的文本...');
        }
        
        // ESC关闭
        if (e.key === 'Escape') {
            const box = document.getElementById('ollama-translation-box');
            if (box) box.remove();
        }
    });

    // 初始化
    console.log('🌐 Ollama智能翻译助手已启动');
    console.log('📋 复制自动翻译:', config.autoShow ? '已启用' : '已禁用');
    console.log('⌨️ 快捷键: Ctrl+Shift+T 手动打开');
    console.log('⚙️ 当前配置:', config);

})();
