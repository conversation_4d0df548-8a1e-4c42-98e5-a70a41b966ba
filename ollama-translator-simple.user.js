// ==UserScript==
// @name         Ollama翻译助手-简化版
// @namespace    http://tampermonkey.net/
// @version      1.1
// @description  复制文字后自动弹出翻译框，连接ollama本地模型（简化版）
// <AUTHOR>
// @match        *://*/*
// @grant        GM_setValue
// @grant        GM_getValue
// @grant        GM_xmlhttpRequest
// @run-at       document-end
// ==/UserScript==

(function() {
    'use strict';

    // 配置
    const config = {
        ollamaUrl: GM_getValue('ollamaUrl', 'http://localhost:11434'),
        model: GM_getValue('model', 'llama3.2'),
        prompt: GM_getValue('prompt', '请将以下文本翻译成中文，如果已经是中文则翻译成英文：'),
        autoShow: GM_getValue('autoShow', true),
        minTextLength: GM_getValue('minTextLength', 3)
    };

    let translationBox = null;
    let lastCopiedText = '';
    let isProcessing = false;

    // 创建翻译框
    function createTranslationBox() {
        const box = document.createElement('div');
        box.innerHTML = `
            <div id="ollama-box" style="
                position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%);
                width: 500px; max-width: 90vw; max-height: 80vh;
                background: white; border: 2px solid #4CAF50; border-radius: 10px;
                box-shadow: 0 4px 20px rgba(0,0,0,0.3); z-index: 10000;
                font-family: Arial, sans-serif; display: none; overflow: hidden;
            ">
                <div style="background: #4CAF50; color: white; padding: 10px 15px; 
                           display: flex; justify-content: space-between; align-items: center;">
                    <span>🌐 Ollama翻译助手</span>
                    <div>
                        <button id="settings-btn" style="background: none; border: none; color: white; 
                               cursor: pointer; padding: 5px; margin-right: 5px;">⚙️</button>
                        <button id="close-btn" style="background: none; border: none; color: white; 
                               cursor: pointer; padding: 5px;">✕</button>
                    </div>
                </div>
                <div style="padding: 15px; max-height: calc(80vh - 60px); overflow-y: auto;">
                    <div style="margin-bottom: 15px;">
                        <label style="display: block; margin-bottom: 5px; font-weight: bold;">原文：</label>
                        <textarea id="source-text" placeholder="在此输入或复制文本..." style="
                            width: 100%; height: 100px; padding: 8px; border: 1px solid #ddd;
                            border-radius: 4px; resize: vertical; font-size: 14px; box-sizing: border-box;
                        "></textarea>
                    </div>
                    <div style="margin-bottom: 15px;">
                        <label style="display: block; margin-bottom: 5px; font-weight: bold;">译文：</label>
                        <div id="translation-result" style="
                            width: 100%; min-height: 120px; max-height: 300px; padding: 8px;
                            border: 1px solid #ddd; border-radius: 4px; background: #f9f9f9;
                            font-size: 14px; line-height: 1.5; white-space: pre-wrap;
                            box-sizing: border-box; overflow-y: auto; word-wrap: break-word;
                        ">等待翻译...</div>
                    </div>
                    <div style="display: flex; gap: 10px; justify-content: center;">
                        <button id="translate-btn" style="
                            padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer;
                            background: #4CAF50; color: white; font-size: 14px;
                        ">翻译</button>
                        <button id="copy-result-btn" style="
                            padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer;
                            background: #2196F3; color: white; font-size: 14px;
                        ">复制结果</button>
                    </div>
                </div>
            </div>
        `;
        document.body.appendChild(box);
        return box.firstElementChild;
    }

    // 调用Ollama API
    async function callOllama(text) {
        return new Promise((resolve, reject) => {
            const requestData = {
                model: config.model,
                prompt: config.prompt + '\n\n' + text,
                stream: false
            };

            GM_xmlhttpRequest({
                method: 'POST',
                url: config.ollamaUrl + '/api/generate',
                headers: { 'Content-Type': 'application/json' },
                data: JSON.stringify(requestData),
                timeout: 30000,
                onload: function(response) {
                    try {
                        const result = JSON.parse(response.responseText);
                        if (result.response) {
                            resolve(result.response.trim());
                        } else {
                            reject('API返回格式错误');
                        }
                    } catch (e) {
                        reject('解析响应失败: ' + e.message);
                    }
                },
                onerror: () => reject('请求失败，请检查Ollama服务是否运行'),
                ontimeout: () => reject('请求超时')
            });
        });
    }

    // 执行翻译
    async function performTranslation(text) {
        if (isProcessing) return;
        
        isProcessing = true;
        const resultDiv = document.getElementById('translation-result');
        const translateBtn = document.getElementById('translate-btn');
        
        resultDiv.textContent = '翻译中...';
        translateBtn.disabled = true;
        translateBtn.textContent = '翻译中...';
        
        try {
            const translation = await callOllama(text);
            resultDiv.textContent = translation;
        } catch (error) {
            resultDiv.textContent = '翻译失败: ' + error;
        } finally {
            isProcessing = false;
            translateBtn.disabled = false;
            translateBtn.textContent = '翻译';
        }
    }

    // 显示翻译框
    function showTranslationBox(text = '') {
        if (!translationBox) {
            translationBox = createTranslationBox();
            setupEventListeners();
        }
        
        const sourceText = document.getElementById('source-text');
        const resultDiv = document.getElementById('translation-result');
        
        if (text) {
            sourceText.value = text;
            resultDiv.textContent = '准备翻译...';
        }
        
        translationBox.style.display = 'block';
        
        // 自动翻译
        if (text && text.length >= config.minTextLength) {
            setTimeout(() => performTranslation(text), 500);
        }
    }

    // 隐藏翻译框
    function hideTranslationBox() {
        if (translationBox) {
            translationBox.style.display = 'none';
        }
    }

    // 设置事件监听器
    function setupEventListeners() {
        document.getElementById('close-btn').addEventListener('click', hideTranslationBox);
        
        document.getElementById('translate-btn').addEventListener('click', () => {
            const text = document.getElementById('source-text').value.trim();
            if (text) performTranslation(text);
        });
        
        document.getElementById('copy-result-btn').addEventListener('click', () => {
            const result = document.getElementById('translation-result').textContent;
            if (result && !result.includes('等待') && !result.includes('翻译中')) {
                navigator.clipboard.writeText(result).then(() => {
                    const btn = document.getElementById('copy-result-btn');
                    const originalText = btn.textContent;
                    btn.textContent = '已复制!';
                    setTimeout(() => btn.textContent = originalText, 1000);
                });
            }
        });
        
        document.getElementById('settings-btn').addEventListener('click', showSettings);
    }

    // 简单设置
    function showSettings() {
        const newUrl = prompt('Ollama服务地址:', config.ollamaUrl);
        if (newUrl) {
            config.ollamaUrl = newUrl;
            GM_setValue('ollamaUrl', newUrl);
        }
        
        const newModel = prompt('模型名称:', config.model);
        if (newModel) {
            config.model = newModel;
            GM_setValue('model', newModel);
        }
        
        const newPrompt = prompt('提示词:', config.prompt);
        if (newPrompt) {
            config.prompt = newPrompt;
            GM_setValue('prompt', newPrompt);
        }
    }

    // 监听复制事件
    document.addEventListener('copy', (e) => {
        if (!config.autoShow) return;
        
        const selection = window.getSelection();
        if (selection && selection.toString()) {
            const copiedText = selection.toString().trim();
            
            if (copiedText && 
                copiedText.length >= config.minTextLength && 
                copiedText !== lastCopiedText &&
                copiedText.length < 5000) {
                
                lastCopiedText = copiedText;
                console.log('🎯 检测到复制:', copiedText.substring(0, 30) + '...');
                
                setTimeout(() => showTranslationBox(copiedText), 300);
            }
        }
    });

    // 快捷键
    document.addEventListener('keydown', (e) => {
        if (e.ctrlKey && e.shiftKey && e.key === 'T') {
            e.preventDefault();
            showTranslationBox();
        }
        if (e.key === 'Escape') {
            hideTranslationBox();
        }
    });

    // 初始化
    console.log('🌐 Ollama翻译助手-简化版已加载');
    console.log('📋 复制文字自动翻译:', config.autoShow ? '已启用' : '已禁用');
    console.log('⌨️ 快捷键: Ctrl+Shift+T');

})();
